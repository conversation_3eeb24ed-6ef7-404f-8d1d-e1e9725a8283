#!/bin/bash

# ES多实例测试脚本演示版本
# 这个版本展示了如何使用新的参数解析功能

# 脚本默认配置
username="root"
password="damon123"
remote_hosts=()
instance_count=1
spec=""
CORES_PER_INSTANCE=16
MEMORY_SIZE="32g"

# 显示帮助信息
show_help() {
    cat << EOF
Elasticsearch 多实例测试脚本

用法: $0 [选项]

选项:
    -n, --instances NUM     设置实例数量 (默认: 1)
    -i, --ips "IP1,IP2,IP3" 设置IP地址列表，用逗号分隔
                           例如: "**********,**********,**********"
    -s, --spec SPEC        设置规格，格式为 XCYg (如: 16C32G)
                           X为CPU核心数，Y为内存大小(GB)
    -h, --help             显示此帮助信息

示例:
    $0 -n 3 -i "**********,**********,**********" -s "16C32G"
    $0 --instances 2 --ips "**********,**********" --spec "8C16G"

说明:
    - 实例数: 控制for循环中的实例数量
    - IP列表: 会被解析为数组格式 remote_hosts=("IP1" "IP2" "IP3")
    - 规格: 16C32G 会设置 CORES_PER_INSTANCE=16 和内存为32g
EOF
}

# 解析规格参数
parse_spec() {
    local spec_input="$1"
    if [[ $spec_input =~ ^([0-9]+)C([0-9]+)G$ ]]; then
        CORES_PER_INSTANCE="${BASH_REMATCH[1]}"
        MEMORY_SIZE="${BASH_REMATCH[2]}g"
        echo "解析规格: CPU核心数=${CORES_PER_INSTANCE}, 内存=${MEMORY_SIZE}"
    else
        echo "错误: 规格格式不正确。请使用格式如 '16C32G'"
        exit 1
    fi
}

# 解析IP列表
parse_ips() {
    local ip_string="$1"
    IFS=',' read -ra remote_hosts <<< "$ip_string"
    echo "解析IP列表: ${remote_hosts[*]}"
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--instances)
            instance_count="$2"
            shift 2
            ;;
        -i|--ips)
            parse_ips "$2"
            shift 2
            ;;
        -s|--spec)
            parse_spec "$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必要参数
if [[ ${#remote_hosts[@]} -eq 0 ]]; then
    echo "错误: 必须指定IP地址列表"
    show_help
    exit 1
fi

echo "配置信息:"
echo "  实例数量: $instance_count"
echo "  IP地址列表: ${remote_hosts[*]}"
echo "  CPU核心数: $CORES_PER_INSTANCE"
echo "  内存大小: $MEMORY_SIZE"
echo ""

# 演示函数 - 展示参数如何被使用
demo_test() {
    echo "=== 演示测试开始 ==="
    
    # 演示实例数循环
    echo "1. 实例数循环演示:"
    for ((i=1; i<=$instance_count; i++)); do
        echo "  处理实例 $i"
    done
    
    # 演示IP数组使用
    echo ""
    echo "2. IP地址数组演示:"
    echo "  remote_hosts数组内容:"
    for ip in "${remote_hosts[@]}"; do
        echo "    - $ip"
    done
    
    # 演示规格参数使用
    echo ""
    echo "3. 规格参数演示:"
    echo "  CORES_PER_INSTANCE=$CORES_PER_INSTANCE"
    echo "  JVM内存配置: echo \"-Xms$MEMORY_SIZE\" >> \"\$CONFIG_DIR/jvm.options\""
    
    # 演示目标主机字符串构建
    echo ""
    echo "4. 目标主机字符串构建演示:"
    target_host=""
    for host in "${remote_hosts[@]}"; do
        target_host+="${host}:9200,"
    done
    target_host=$(echo "$target_host" | sed 's/,\s*$//')
    echo "  target_hosts=$target_host"
    
    echo "=== 演示测试结束 ==="
}

# 主函数
main() {
    echo "ES多实例测试脚本 - 演示版本"
    echo "================================"
    demo_test
    echo ""
    echo "提示: 这是演示版本，实际的ES测试功能请使用完整版本的test.sh脚本"
}

# 运行主函数
main
