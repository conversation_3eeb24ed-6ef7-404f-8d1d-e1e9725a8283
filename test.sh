#!/bin/bash

# 脚本默认配置
username="root"
password="damon123"
remote_hosts=()
instance_count=1
spec=""
CORES_PER_INSTANCE=16
MEMORY_SIZE="32g"

# 显示帮助信息
show_help() {
    cat << EOF
Elasticsearch 多实例测试脚本

用法: $0 [选项]

选项:
    -n, --instances NUM     设置实例数量 (默认: 1)
    -i, --ips "IP1,IP2,IP3" 设置IP地址列表，用逗号分隔
                           例如: "**********,**********,**********"
    -s, --spec SPEC        设置规格，格式为 XCYg (如: 16C32G)
                           X为CPU核心数，Y为内存大小(GB)
    -h, --help             显示此帮助信息

示例:
    $0 -n 3 -i "**********,**********,**********" -s "16C32G"
    $0 --instances 2 --ips "**********,**********" --spec "8C16G"

说明:
    - 实例数: 控制for循环中的实例数量
    - IP列表: 会被解析为数组格式 remote_hosts=("IP1" "IP2" "IP3")
    - 规格: 16C32G 会设置 CORES_PER_INSTANCE=16 和内存为32g
EOF
}

# 解析规格参数
parse_spec() {
    local spec_input="$1"
    if [[ $spec_input =~ ^([0-9]+)C([0-9]+)G$ ]]; then
        CORES_PER_INSTANCE="${BASH_REMATCH[1]}"
        MEMORY_SIZE="${BASH_REMATCH[2]}g"
        echo "解析规格: CPU核心数=${CORES_PER_INSTANCE}, 内存=${MEMORY_SIZE}"
    else
        echo "错误: 规格格式不正确。请使用格式如 '16C32G'"
        exit 1
    fi
}

# 解析IP列表
parse_ips() {
    local ip_string="$1"
    IFS=',' read -ra remote_hosts <<< "$ip_string"
    echo "解析IP列表: ${remote_hosts[*]}"
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--instances)
            instance_count="$2"
            shift 2
            ;;
        -i|--ips)
            parse_ips "$2"
            shift 2
            ;;
        -s|--spec)
            parse_spec "$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必要参数
if [[ ${#remote_hosts[@]} -eq 0 ]]; then
    echo "错误: 必须指定IP地址列表"
    show_help
    exit 1
fi

echo "配置信息:"
echo "  实例数量: $instance_count"
echo "  IP地址列表: ${remote_hosts[*]}"
echo "  CPU核心数: $CORES_PER_INSTANCE"
echo "  内存大小: $MEMORY_SIZE"
echo ""

# 当前日期
current_date=$(date +%Y%m%d)
# 要操作的磁盘设备
disk_device="/dev/nvme0n1p1"
# 待执行的命令
command="numactl -C 16-31 esrally race --offline --pipeline=benchmark-only --track=geonames --target-hosts=************:9200 --include-tasks=\"delete-index,create-index,check-cluster-health,index-append,term,phrase,scroll\" --report-format=markdown --race-id=20240105 --kill-running-processes"

# SSH 远程执行命令函数
ssh_exec() {
    sshpass -p "$password" ssh -o StrictHostKeyChecking=no "$username@$1" "$2"
}

# 注意: ip_get函数已被参数解析替代，remote_hosts现在通过命令行参数设置
# 保留此函数以兼容现有代码，但不再修改remote_hosts数组
ip_get(){
	cpu_vendor=$1
	echo "机器为 $cpu_vendor"
	echo "使用命令行指定的IP列表: ${remote_hosts[*]}"
}


#执行测试
test_exec() {

#	/root/miniconda3/bin/conda init
#	source /root/.bashrc
	conda activate esrally

	current_date=$(date +%Y%m%d)
	systemctl stop irqbalance.service
	mkdir -p /opt/irq_script/
	yes | cp /nas/damon.xu/script/change*.sh /opt/irq_script/
	target_host=""

	for host in "${remote_hosts[@]}"; do
		target_host+="${host}:9200,"
	done

	# 去掉最后一个空格
	target_host=$(echo "$target_host" | sed 's/,\s*$//')

	cpu_vendor=$(dmidecode -t processor | grep 'Manufacturer'|uniq | awk -F ':' '{print $2}')
	if [[ $cpu_vendor =~ "AMD" ]]; then
		/opt/irq_script/changeAMDLowLoadIRQ.sh
		for((i=1;i<=$instance_count;i++));do
			numactl -C 30-45 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$target_host --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/AMD/SingleSocket/instance$i/http_logs/low_load/test$i.md"
			sleep 10
		done
	elif [[ $cpu_vendor =~ "Ampere" ]]; then
		/opt/irq_script/changeAmpereLowLoadIRQ.sh
		for((i=1;i<=$instance_count;i++));do
			numactl -C 30-45 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$target_host --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/Ampere/${NODE_IP}/SingleSocket/instance$i/http_logs/low_load/test$i.md"
			sleep 10
		done
	elif [[ $cpu_vendor =~ "Intel" ]]; then
		/opt/irq_script/changeIntelLowLoadIRQ.sh
		for((i=1;i<=$instance_count;i++));do
			numactl -C $(seq -s, 16 2 $((16 + 2 * 15))) --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$target_host --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/Intel/SingleSocket/instance$i/http_logs/low_load/test$i.md"
			sleep 10
		done
	else
		/opt/irq_script/changeAmpereLowLoadIRQ.sh
		for((i=1;i<=$instance_count;i++));do
			numactl -C 0-63 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$target_host --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/Kunpeng/SingleSocket/instance$i/http_logs/low_load/test$i.md"
			sleep 10
		done
	fi
}

test_pall_exec() {

#	/root/miniconda3/bin/conda init
#	source /root/.bashrc
	conda activate esrally

	count=$1
	declare -a nodeList
    	declare -a portList
    	declare -a node_http_port_list

    	for ((k=1; k<=$1; k++)); do
                for node in "${remote_hosts[@]}"; do
                        node_http="${node}:$((9200 + 10 * count + k - 1))"
                        nodeList+=("node${k}_${node}")  # 区分不同节点
                        node_http_port_list+=("${node_http}")
                done
    	done
    	node_http_port_list_str=$(IFS=,; echo "${node_http_port_list[*]}")
    	echo "node_http = ${node_http_port_list_str}"

	current_date=$(date +%Y%m%d)
#	systemctl stop irqbalance.service
#	mkdir -p /opt/irq_script/
#	yes | cp /nas/damon.xu/script/change*.sh /opt/irq_script/

	cpu_vendor=$(dmidecode -t processor | grep 'Manufacturer'|uniq | awk -F ':' '{print $2}')
	if [[ $cpu_vendor =~ "AMD" ]]; then
#		/opt/irq_script/changeAMDLowLoadIRQ.sh
		for((i=1;i<=1;i++));do
			numactl -C 0-80 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$node_http_port_list_str --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/${2}/paraller_${1}/test$i.md"
			sleep 10
		done
	elif [[ $cpu_vendor =~ "Ampere" ]]; then
#		/opt/irq_script/changeAmpereLowLoadIRQ.sh
		for((i=1;i<=1;i++));do
			numactl -C 0-80 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$node_http_port_list_str --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/${2}/paraller_${1}/test$i.md"
			sleep 10
		done
	elif [[ $cpu_vendor =~ "Intel" ]]; then
#		/opt/irq_script/changeIntelLowLoadIRQ.sh
		for((i=1;i<=1;i++));do
			numactl -C $(seq -s, 16 2 $((16 + 2 * 15))) --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$node_http_port_list_str --include-tasks="delete-index,create-index,check-cluster-health,index-append,term,scroll" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/${2}/paraller_${1}/test$i.md"
			sleep 10
		done
	else
#		/opt/irq_script/changeAmpereLowLoadIRQ.sh
		for((i=1;i<=1;i++));do
			numactl -C 0-80 --membind=0 esrally race --offline --pipeline=benchmark-only --track=http_logs --target-hosts=$node_http_port_list_str --include-tasks="delete-index,create-index,check-cluster-health,index-append" --report-format=markdown --race-id=$current_date --kill-running-processes --report-file="/home/<USER>/$current_date/${2}/paraller_${1}/test$i.md"
			sleep 10
		done
	fi
}

#关闭集群es
shutdown_es_exec() {
    for host in "${remote_hosts[@]}"; do
        # 关闭ES
	echo "host = $host "
	echo "$remote_hosts"
        echo "正在关闭 $host ES"
        ssh "$host" "jps|grep lasticsearch |awk '{print $1}'|xargs kill -9 ;rm -rf /data1/*"
    done
    sleep 10
}

node_list=""

paraller_test_config_single_marchine(){
    count=$instance_count
    echo "count = $count"
    jps|grep lasticsearch |awk '{print $1}'|xargs kill -9
    CORES_PER_INSTANCE=16
    echo "NODE_LIST = ${nodeListStr}"
    echo 3 > /proc/sys/vm/drop_caches
    declare -a nodeList
    declare -a portList
    declare -a node_http_port_list

    for ((k=1; k<=instance_count; k++)); do
                for node in "${remote_hosts[@]}"; do
                        node_http="${node}:$((9200 + 10 * count + k - 1))"
                        tcp_port="${node}:$((8200 + 10 * instance_count + k - 1))"
                        nodeList+=("node${k}_${node}")  # 区分不同节点
                        portList+=("${tcp_port}")
                        node_http_port_list+=("${node_http}")
		done
    done
    nodeListStr=$(printf '"%s",' "${nodeList[@]}")
    nodeListStr="${nodeListStr%,}"
    echo "nodeListStr = ${nodeListStr}"
    portListStr=$(printf '"%s",' "${portList[@]}")
    portListStr="${portListStr%,}"
    echo "port_list =  ${portList}"
    node_http_port_list_str=$(printf '"%s",' "${node_http_port_list[@]}")
    node_http_port_list_str="${node_http_port_list_str%,}"
    echo "node_http = ${node_http_port_list_str}"
    node_list="${node_http_port_list_str%,}"

    for ((j=1; j<=count; j++)); do
        HTTP_PORT=$((9200 + 10 * count + j - 1))
        TCP_PORT=$((8200 + 10 * count + j - 1))

        echo "HTTP_PORT = $HTTP_PORT"
        echo "TCP_PORT = $TCP_PORT"

        CONFIG_DIR="/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$j"
        DATA_PATH="/data1/es/paraller_test/paraller$count/instance${j}/data"
        LOGS_PATH="/data1/es/paraller_test/paraller$count/instance${j}/logs"
        CONFIG_FILE="$CONFIG_DIR/elasticsearch.yml"
        CLUSTER_NAME="es_paraller_test_instance$count"

        echo "CLUSTER_NAME = $CLUSTER_NAME"
        mkdir -p "$CONFIG_DIR" "$DATA_PATH" "$LOGS_PATH"
        rm -rf "$DATA_PATH"/*
        echo "j = ${j}"
        cat > "$CONFIG_FILE" <<EOF
cluster.name: $CLUSTER_NAME
node.name: node${j}_$matched_ip
network.host: $matched_ip
transport.port: $TCP_PORT
http.port: $HTTP_PORT
path.data: $DATA_PATH
path.logs: $LOGS_PATH
discovery.seed_hosts: [${portListStr}]
cluster.initial_master_nodes: [$nodeListStr]
xpack.security.enabled: false
xpack.security.transport.ssl.enabled: false
xpack.security.http.ssl.enabled: false
http.netty.worker_count: 4
http.cors.enabled: true
http.cors.allow-origin: '*'
EOF

        cp /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/jvm.options "$CONFIG_DIR"
        cp /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/log4j2.properties "$CONFIG_DIR"
        echo "-Xms$MEMORY_SIZE" >> "$CONFIG_DIR/jvm.options"

        cat "$CONFIG_FILE"
    done

    useradd es
    chown -R es:es /data1/es
    chown -R es:es /opt/PostSilicon-Elasticsearch

    start=0
    end=0
    intelstart1=64
    AMDstart=128
    cpu_vendor=$(dmidecode -t processor | grep 'Manufacturer' | uniq)

    for ((a=1; a<=count; a++)); do
        if [[ "$cpu_vendor" =~ "Intel" ]]; then
            end=$((start + 2 * 7))
            CPU=$(seq -s, $start 2 $end)
            start=$((end+2))
            end1=$((intelstart1 + 2 * 7))
            CPU1=$(seq -s, $intelstart1 2 $end1)
            intelstart1=$((end1+2))
            CPU2="$CPU,$CPU1"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU2 /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        elif [[ "$cpu_vendor" =~ "Ampere" ]]; then
            start=$((CORES_PER_INSTANCE * (a - 1)))
            end=$(((CORES_PER_INSTANCE * a) - 1))
            CPU="$start-$end"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        elif [[ "$cpu_vendor" =~ "AMD" ]]; then
            start=$((CORES_PER_INSTANCE/2 * (a - 1)))
            end=$(((CORES_PER_INSTANCE/2 * a) - 1))
            start1=$((AMDstart + CORES_PER_INSTANCE/2 * (a - 1)))
            end1=$(((AMDstart + CORES_PER_INSTANCE/2 * a) - 1))
            CPU="$start-$end,$start1-$end1"
            su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        else
			start=$((CORES_PER_INSTANCE * (a - 1)))
			end=$(((CORES_PER_INSTANCE * a) - 1))
			CPU="$start-$end"
			su - es -c "ES_PATH_CONF=/opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/config/paraller_test/paraller$count/instance$a numactl -C $CPU /opt/PostSilicon-Elasticsearch/elasticsearch-8.9.0/bin/elasticsearch -d"
        fi
    done

}

paraller_test(){
	# tqs
	for ((i=1; i<=$instance_count; i++)); do
  	  shutdown_es_exec
	  echo "i = ${i}"
	  for remote_ip in "${remote_hosts[@]}"; do
		if [[ "$ip" == "$remote_ip" ]]; then
			matched_ip="$ip"
			break 2  # 匹配到后直接跳出所有循环
		fi
		echo "ip = $remote_ip"
		ssh $remote_ip "sysctl -w vm.max_map_count=262144;cp /nas/damon.xu/xianxingdu/es_cluster_test_hongmin_xianxingdu.sh /opt;export matched_ip=$remote_ip;export instance_count=${i};source /opt/es_cluster_test_hongmin_xianxingdu.sh;ip_get $1;paraller_test_config_single_marchine"
	done

	sleep 10

#	ssh *********** "export node_http_port_list_str=${node_list};source /opt/es_cluster_test.sh;ip_get $1;test_pall_exec ${i} $1"

done

}


start_paraller_test(){
	i=0
	echo "ip = $remote_hosts"
	for host in "${remote_hosts[@]}";do
		ssh "$host" "yes | cp /nas/damon.xu/cluster_script/es_env.sh /opt;source /opt/es_env.sh;download_es;ip_get $1;paraller_test"
		i=$((i+1))
	done
}

download_es(){
	cd /opt
	mkdir -p PostSilicon-Elasticsearch
	# 获取当前系统的架构信息
	ARCH=$(uname -m)

	cd PostSilicon-Elasticsearch

	# 如果当前系统是x86架构
	if [ "$ARCH" == "x86_64" ]; then
		echo "当前系统是x86架构"
		if [ ! -f "elasticsearch-8.9.0-linux-x86_64.tar.gz" ]; then
			echo "elasticsearch-8.9.0-linux-x86_64.tar.gz 文件不存在，开始下载..."
			# 执行下载语句
			wget http://************/bigdata/Elasticsearch/elasticsearch-8.9.0-linux-x86_64.tar.gz
			tar -zxvf elasticsearch-8.9.0-linux-x86_64.tar.gz
		else
			echo "elasticsearch-8.9.0-linux-x86_64.tar.gz 文件已存在，无需下载。"
		fi

	# 如果当前系统是ARM架构
	elif [ "$ARCH" == "aarch64" ]; then
		echo "当前系统是ARM架构"
		if [ ! -f "elasticsearch-8.9.0-linux-aarch64.tar.gz" ]; then
			echo "elasticsearch-8.9.0-linux-aarch64.tar.gz 文件不存在，开始下载..."
			# 执行下载语句
			wget http://************/bigdata/Elasticsearch/elasticsearch-8.9.0-linux-aarch64.tar.gz
			tar -zxvf elasticsearch-8.9.0-linux-aarch64.tar.gz
		else
			echo "elasticsearch-8.9.0-linux-aarch64.tar.gz 文件已存在，无需下载。"
		fi
	else
		echo "未知系统架构：$ARCH"
	fi
}


start_test(){

  echo "正在进行ES 线性度测试"
	ip_get $1
	#esrally_env

	paraller_test $1

	echo "线性度测试结束"

}

# 主函数调用
main() {
    echo "开始ES多实例测试..."
    echo "配置参数:"
    echo "  实例数量: $instance_count"
    echo "  IP地址列表: ${remote_hosts[*]}"
    echo "  CPU核心数: $CORES_PER_INSTANCE"
    echo "  内存大小: $MEMORY_SIZE"
    echo ""

    # 这里可以调用你需要的测试函数
    # 例如: start_test 或 paraller_test
    echo "请根据需要调用相应的测试函数:"
    echo "  - start_test: 开始线性度测试"
    echo "  - paraller_test: 开始并行测试"
    echo "  - test_exec: 执行单个测试"
}

# 如果有参数则调用主函数
if [[ ${#remote_hosts[@]} -gt 0 ]]; then
    main
fi


